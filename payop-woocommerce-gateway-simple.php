<?php
/**
 * Plugin Name: PayOp WooCommerce Payment Gateway (Simple)
 * Plugin URI: https://github.com/payop/woocommerce-gateway
 * Description: Accept payments via PayOp with 122+ payment methods using Direct Integration (bypassing PayOp hosted checkout)
 * Version: 1.0.0
 * Author: PayOp
 * Author URI: https://payop.com
 * License: GPL v2 or later
 * License URI: https://www.gnu.org/licenses/gpl-2.0.html
 * Text Domain: payop-woocommerce
 * Domain Path: /languages
 * Requires at least: 6.0
 * Tested up to: 6.4
 * Requires PHP: 8.0
 * WC requires at least: 7.0
 * WC tested up to: 8.5
 * Network: false
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Define plugin constants
define('PAYOP_WC_VERSION', '1.0.0');
define('PAYOP_WC_PLUGIN_FILE', __FILE__);
define('PAYOP_WC_PLUGIN_DIR', plugin_dir_path(__FILE__));
define('PAYOP_WC_PLUGIN_URL', plugin_dir_url(__FILE__));
define('PAYOP_WC_PLUGIN_BASENAME', plugin_basename(__FILE__));

/**
 * Simple PayOp Gateway Class
 */
class WC_Gateway_PayOp extends WC_Payment_Gateway {

    public function __construct() {
        $this->id = 'payop';
        $this->icon = '';
        $this->has_fields = true;
        $this->method_title = 'PayOp Payment Gateway';
        $this->method_description = 'Accept payments via PayOp with 122+ payment methods using Direct Integration';

        $this->supports = ['products'];

        $this->init_form_fields();
        $this->init_settings();

        $this->title = $this->get_option('title');
        $this->description = $this->get_option('description');
        $this->enabled = $this->get_option('enabled');
        $this->public_key = $this->get_option('public_key');
        $this->secret_key = $this->get_option('secret_key');
        $this->jwt_token = $this->get_option('jwt_token');
        $this->project_id = $this->get_option('project_id');

        add_action('woocommerce_update_options_payment_gateways_' . $this->id, [$this, 'process_admin_options']);
    }

    public function init_form_fields() {
        $this->form_fields = [
            'enabled' => [
                'title' => 'Enable/Disable',
                'type' => 'checkbox',
                'label' => 'Enable PayOp Payment Gateway',
                'default' => 'no'
            ],
            'title' => [
                'title' => 'Title',
                'type' => 'text',
                'description' => 'This controls the title which the user sees during checkout.',
                'default' => 'PayOp Payment Gateway',
                'desc_tip' => true,
            ],
            'description' => [
                'title' => 'Description',
                'type' => 'textarea',
                'description' => 'This controls the description which the user sees during checkout.',
                'default' => 'Pay securely using PayOp with 122+ payment methods',
                'desc_tip' => true,
            ],
            'public_key' => [
                'title' => 'Public Key',
                'type' => 'text',
                'description' => 'Your PayOp public key (e.g., application-606)',
                'default' => '',
                'desc_tip' => true,
            ],
            'secret_key' => [
                'title' => 'Secret Key',
                'type' => 'password',
                'description' => 'Your PayOp secret key for signature generation',
                'default' => '',
                'desc_tip' => true,
            ],
            'jwt_token' => [
                'title' => 'JWT Token',
                'type' => 'textarea',
                'description' => 'Your PayOp JWT token for API authentication',
                'default' => '',
                'desc_tip' => true,
            ],
            'project_id' => [
                'title' => 'Project ID',
                'type' => 'text',
                'description' => 'Your PayOp project ID (e.g., 606)',
                'default' => '',
                'desc_tip' => true,
            ],
        ];
    }

    public function is_available() {
        if ('yes' !== $this->enabled) {
            return false;
        }

        // For now, just check if enabled - we'll add credential validation later
        return true;
    }

    public function payment_fields() {
        if ($this->description) {
            echo wpautop(wp_kses_post($this->description));
        }

        echo '<div id="payop-payment-form">';
        echo '<p>PayOp payment gateway is configured. Payment methods will be loaded here.</p>';
        echo '<input type="hidden" name="payop_payment_method" value="1" />';
        echo '</div>';
    }

    public function validate_fields() {
        // Basic validation for now
        return true;
    }

    public function process_payment($order_id) {
        $order = wc_get_order($order_id);

        if (!$order) {
            return [
                'result' => 'failure',
                'messages' => 'Order not found.'
            ];
        }

        // For now, just mark as pending and redirect to thank you page
        $order->update_status('pending', 'Awaiting PayOp payment (test mode)');
        
        // Reduce stock
        wc_reduce_stock_levels($order_id);
        
        // Remove cart
        WC()->cart->empty_cart();

        return [
            'result' => 'success',
            'redirect' => $this->get_return_url($order)
        ];
    }
}

/**
 * Add PayOp Gateway to WooCommerce
 */
function add_payop_gateway($gateways) {
    $gateways[] = 'WC_Gateway_PayOp';
    return $gateways;
}

/**
 * Initialize PayOp Gateway
 */
function init_payop_gateway() {
    if (!class_exists('WooCommerce')) {
        add_action('admin_notices', function() {
            echo '<div class="notice notice-error"><p>PayOp WooCommerce Gateway requires WooCommerce to be installed and active.</p></div>';
        });
        return;
    }

    add_filter('woocommerce_payment_gateways', 'add_payop_gateway');
}

// Initialize the gateway
add_action('plugins_loaded', 'init_payop_gateway');

/**
 * Plugin activation
 */
register_activation_hook(__FILE__, function() {
    if (!class_exists('WooCommerce')) {
        deactivate_plugins(plugin_basename(__FILE__));
        wp_die('PayOp WooCommerce Gateway requires WooCommerce to be installed and active.');
    }
});
