# PayOp API Payment Integration Documentation

## Overview

This documentation covers the PayOp API payment integration requirements based on systematic analysis and testing of the API endpoints. The focus is on payment/checkout functionality suitable for WooCommerce integration.

## Test Credentials Used

- **Public Key**: application-606
- **Secret Key**: fd6d7b9d6e14146ba064cd3b7afd7a0e
- **Project ID**: 606
- **JWT Token**: eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpZCI6MTUzNCwidGltZSI6MTc1MDU2OTA0NywidHdvRmFjdG9yIjp7InBhc3NlZCI6dHJ1ZX0sInRva2VuSWQiOjYyNDQsImV4cGlyZWRBdCI6MTc1MTMxNzE5OSwicm9sZSI6MSwiYWNjZXNzVG9rZW4iOiJiNzIyMzY4Mjc0M2U3YzYzNjYzZDU1MTUifQ.dbFuV4BKlk84Q5w6SV1DpsYOJ18C0vMHSvJF3orxMCU

## Integration Types

PayOp offers two integration approaches:

### 1. Hosted Page Integration (Recommended for Quick Setup)
- Simple redirect-based flow
- PayOp handles the entire checkout process
- Minimal development effort required
- Automatic compliance and security handling

### 2. Direct Integration (Advanced)
- Custom checkout experience
- Merchant collects payment data
- More control over user experience
- Requires additional development

## Authentication

### Signature Generation
PayOp uses SHA-256 signatures for secure API requests:

**Formula**: `SHA256(amount:currency:order_id:secret_key)`

**Example**:
```bash
echo -n "10.00:EUR:test-order-123:fd6d7b9d6e14146ba064cd3b7afd7a0e" | shasum -a 256
# Result: b3b4b85711da540d9edb74018119a24081fd3ee020352aeeea8a1050a8d10600
```

### JWT Bearer Token
Required for checkout and transaction management endpoints:
```
Authorization: Bearer YOUR_JWT_TOKEN
```

## Core Payment Endpoints

### 1. Get Available Payment Methods

**Endpoint**: `GET /v1/instrument-settings/payment-methods/available-for-application/{APPLICATION_ID}`

**Purpose**: Retrieve payment methods available for the project

**Headers**:
```
Content-Type: application/json
Authorization: Bearer YOUR_JWT_TOKEN
```

**cURL Example**:
```bash
curl -X GET "https://api.payop.com/v1/instrument-settings/payment-methods/available-for-application/606" \
 -H "Content-Type: application/json" \
 -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

**Response Structure**:
```json
{
  "data": [
    {
      "identifier": 700001,
      "type": "cards_international",
      "title": "Cards via PayDo",
      "currencies": ["EUR", "USD", "GBP"],
      "countries": ["US", "GB", "DE", "..."],
      "config": {
        "fields": [
          {
            "name": "email",
            "type": "email",
            "required": true
          },
          {
            "name": "name",
            "type": "text",
            "required": true
          }
        ]
      }
    }
  ],
  "status": 1
}
```

**Key Response Fields**:
- `identifier`: Payment method ID for checkout creation
- `type`: Payment method category (cards_international, bank_transfer, ewallet, etc.)
- `currencies`: Supported currencies
- `countries`: Supported countries
- `config.fields`: Required customer data fields

### 2. Create Invoice

**Endpoint**: `POST /v1/invoices/create`

**Purpose**: Create a payment invoice

**Headers**:
```
Content-Type: application/json
```

**Required Parameters**:
- `publicKey`: Project public key
- `order.id`: Unique order identifier
- `order.amount`: Payment amount (string)
- `order.currency`: Currency code (EUR, USD, etc.)
- `payer.email`: Customer email
- `signature`: SHA-256 signature

**cURL Example**:
```bash
curl -X POST "https://api.payop.com/v1/invoices/create" \
 -H "Content-Type: application/json" \
 -d '{
   "publicKey": "application-606",
   "order": {
     "id": "test-order-123",
     "amount": "10.00",
     "currency": "EUR",
     "items": [
       {
         "id": "1",
         "name": "Test Product",
         "price": "10.00"
       }
     ],
     "description": "Test Payment"
   },
   "signature": "b3b4b85711da540d9edb74018119a24081fd3ee020352aeeea8a1050a8d10600",
   "payer": {
     "email": "<EMAIL>"
   },
   "language": "en",
   "resultUrl": "https://example.com/result",
   "failPath": "https://example.com/fail"
 }'
```

**Success Response**:
```json
{
  "data": "5eb23031-246e-4172-b520-20e8765ab6c2",
  "status": 1
}
```

**Template Expressions**:
- `{{invoiceId}}`: Replaced with PayOp invoice ID
- `{{txid}}`: Replaced with PayOp transaction ID

**Example**:
```
https://example.com/result?invoiceId={{invoiceId}}&txid={{txid}}
```

### 3. Get Invoice Information

**Endpoint**: `GET /v1/invoices/{invoiceID}`

**Purpose**: Retrieve detailed invoice information

**cURL Example**:
```bash
curl -X GET "https://api.payop.com/v1/invoices/5eb23031-246e-4172-b520-20e8765ab6c2" \
 -H "Content-Type: application/json"
```

**Response Structure**:
```json
{
  "data": {
    "identifier": "5eb23031-246e-4172-b520-20e8765ab6c2",
    "status": 0,
    "amount": 10,
    "currency": "EUR",
    "orderIdentifier": "test-order-123",
    "items": [...],
    "payer": {
      "email": "<EMAIL>"
    },
    "createdAt": 1750569323
  },
  "status": 1
}
```

**Invoice Status Codes**:
- `0`: New (created but no action taken)
- `1`: Paid (successfully completed)
- `2`: Overdue (expired, default 24 hours)
- `4`: Pending (awaiting payment)
- `5`: Failed (payment failed)

### 4. Create Checkout Transaction

**Endpoint**: `POST /v1/checkout/create`

**Purpose**: Create a checkout transaction for direct integration

**Headers**:
```
Content-Type: application/json
Authorization: Bearer YOUR_JWT_TOKEN
```

**Required Parameters**:
- `invoiceIdentifier`: Invoice ID from step 2
- `customer`: Customer information
- `paymentMethod`: Payment method identifier

**cURL Example**:
```bash
curl -X POST "https://api.payop.com/v1/checkout/create" \
 -H "Content-Type: application/json" \
 -H "Authorization: Bearer YOUR_JWT_TOKEN" \
 -d '{
   "invoiceIdentifier": "5eb23031-246e-4172-b520-20e8765ab6c2",
   "customer": {
     "email": "<EMAIL>",
     "name": "John Doe",
     "ip": "***********"
   },
   "paymentMethod": 700001,
   "checkStatusUrl": "https://example.com/check-status/{{txid}}"
 }'
```

**Success Response**:
```json
{
  "data": {
    "isSuccess": true,
    "message": "",
    "txid": "b3b0f6ad-170c-5b79-a36e-eeb1f6362ed5"
  },
  "status": 1
}
```

### 5. Check Invoice Status

**Endpoint**: `GET /v1/checkout/check-invoice-status/{invoiceID}`

**Purpose**: Check current payment status (polling)

**Headers**:
```
Content-Type: application/json
Authorization: Bearer YOUR_JWT_TOKEN
```

**cURL Example**:
```bash
curl -X GET "https://api.payop.com/v1/checkout/check-invoice-status/5eb23031-246e-4172-b520-20e8765ab6c2" \
 -H "Content-Type: application/json" \
 -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

**Response Examples**:

**Pending Status**:
```json
{
  "data": {
    "isSuccess": true,
    "status": "pending",
    "form": {
      "method": "GET",
      "url": "https://checkout.payop.com/en/payment/transition/5eb23031-246e-4172-b520-20e8765ab6c2",
      "fields": []
    }
  },
  "status": 1
}
```

**Redirect Required (POST)**:
```json
{
  "data": {
    "isSuccess": true,
    "status": "pending",
    "form": {
      "method": "POST",
      "url": "https://acs.anybank.com/",
      "fields": {
        "PaReq": "encrypted_data",
        "MD": "unique_id",
        "TermUrl": "https://payop.com/v1/url"
      }
    }
  },
  "status": 1
}
```

### 6. Get Transaction Details

**Endpoint**: `GET /v2/transactions/{transactionID}`

**Purpose**: Retrieve detailed transaction information

**Headers**:
```
Content-Type: application/json
Authorization: Bearer YOUR_JWT_TOKEN
```

**cURL Example**:
```bash
curl -X GET "https://api.payop.com/v2/transactions/b3b0f6ad-170c-5b79-a36e-eeb1f6362ed5" \
 -H "Content-Type: application/json" \
 -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

**Response Structure**:
```json
{
  "data": {
    "identifier": "transaction_id",
    "amount": 100,
    "currency": "USD",
    "state": 5,
    "error": "error message",
    "createdAt": **********,
    "orderId": "134666",
    "resultUrl": "https://your.site/result"
  }
}
```

**Transaction State Codes**:
- `1`: New (no actions taken)
- `2`: Accepted (paid successfully)
- `3`: Failed (technical/financial reasons)
- `4`: Pending (awaiting payment)
- `5`: Failed (technical/financial reasons)
- `9`: Pre-approved (submitted, awaiting funds)
- `15`: Timeout (lack of final confirmation)

### 7. Void Transaction

**Endpoint**: `POST /v1/checkout/void`

**Purpose**: Cancel a pending transaction

**Headers**:
```
Content-Type: application/json
Authorization: Bearer YOUR_JWT_TOKEN
```

**cURL Example**:
```bash
curl -X POST "https://api.payop.com/v1/checkout/void" \
 -H "Content-Type: application/json" \
 -H "Authorization: Bearer YOUR_JWT_TOKEN" \
 -d '{
   "invoiceIdentifier": "5eb23031-246e-4172-b520-20e8765ab6c2"
 }'
```

**Success Response**:
```json
{
  "data": {
    "isSuccess": true,
    "message": "",
    "txid": "canceled_transaction_id"
  },
  "status": 1
}
```

## Instant Payment Notifications (IPN)

### IPN Configuration
Configure IPN URLs in your PayOp project settings for real-time payment updates.

### IPN Payload Structure
```json
{
  "invoice": {
    "id": "d024f697-ba2d-456f-910e-4d7fdfd338dd",
    "status": 1,
    "txid": "dca59ca5-be19-470d-9494-9b76944e0241",
    "metadata": {
      "orderId": "test",
      "amount": 3,
      "customerId": 15487
    }
  },
  "transaction": {
    "id": "dca59ca5-be19-470d-9494-9b76944e0241",
    "state": 2,
    "order": {
      "id": "ANY_ORDER_ID"
    },
    "error": {
      "message": "Error message",
      "code": ""
    }
  }
}
```

### IPN Security
**Whitelist these PayOp IP addresses**:
- *************
- *************
- ************
- *************

### IPN Best Practices
1. Always return HTTP 200 OK after successful processing
2. Log every IPN received
3. Ignore duplicates with same data
4. Update transaction status if new status differs
5. Validate IPN source IP addresses

## Error Handling

### Common HTTP Status Codes

**401 Unauthorized**:
```json
{
  "message": "Full authentication is required to access this resource."
}
```

**403 Forbidden**:
```json
{
  "message": "Access denied."
}
```

**404 Not Found**:
```json
{
  "message": "Invoice not found"
}
```

**422 Unprocessable Entity**:
```json
{
  "message": "Method must be enabled to use it"
}
```

**Validation Errors**:
```json
{
  "message": {
    "email": ["This value should not be blank."],
    "password": ["This value should not be blank."]
  }
}
```

## Payment Method Analysis

Based on testing, the project supports 100+ payment methods including:

### International Cards
- **ID**: 700001
- **Type**: cards_international
- **Currencies**: EUR, AUD, CAD, GBP, USD, DKK
- **Global coverage**: 100+ countries

### Bank Transfers
- **Pay by bank**: Multiple European countries
- **PSE**: Colombia banking system
- **SEPA**: European bank transfers

### E-Wallets
- **PayDo E-Wallet**: Multi-currency support
- **GCash**: Philippines
- **Maya**: Philippines

### Crypto Payments
- **ID**: 6110
- **Global coverage**: 150+ countries
- **Currency**: EUR

### Cash Payments
- Various regional cash payment networks
- Country-specific solutions

## Integration Recommendations

### For WooCommerce Integration

1. **Use Hosted Page Integration** for quick deployment
2. **Implement proper signature generation** for security
3. **Configure IPN endpoints** for real-time updates
4. **Handle all transaction states** appropriately
5. **Implement proper error handling** for all scenarios
6. **Use template expressions** in result URLs for tracking

### Security Considerations

1. **Never expose secret keys** in frontend code
2. **Validate all IPN requests** by IP whitelist
3. **Use HTTPS** for all API communications
4. **Implement proper signature validation**
5. **Store sensitive data securely**

### Testing Approach

1. **Test with provided credentials** in sandbox
2. **Verify signature generation** accuracy
3. **Test all payment flows** end-to-end
4. **Validate IPN handling** thoroughly
5. **Test error scenarios** and edge cases

## Conclusion

The PayOp API provides a comprehensive payment solution with multiple integration options. The hosted page integration offers the quickest path to production, while direct integration provides maximum customization. Proper implementation of signatures, IPN handling, and error management ensures a robust payment system suitable for WooCommerce integration.
